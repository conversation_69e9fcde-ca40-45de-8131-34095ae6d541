# DPML动态术语协议 (Dynamic Terminology Protocol)

> **TL;DR:** DPML采用动态术语理解机制，AI需根据`#术语`在当前协议和对话中的上下文自主理解其含义。

## 🎯 核心理念：上下文即定义

为极致精简和发挥AI的动态理解能力，PromptX不再提供预定义的术语表。所有以`#`标记的术语（如 `#标签`、`#协议绑定`），AI需要：

1.  **依赖当前协议上下文**：术语的精确含义由其所在的协议文件内容决定。
2.  **结合对话历史**：根据当前对话的上下文动态推断和理解术语。
3.  **关联相关概念**：将遇到的`#术语`与协议中其他概念和机制关联。
4.  **主动澄清（可选）**：如遇模糊不清的术语，可主动提问或要求举例。

## 💡 设计原则

- **AI自主理解优先**：最大化信任和利用AI的上下文推理能力。
- **协议自解释性**：协议内容本身应足够清晰，以支持AI正确理解术语。
- **动态适应性**：术语含义可根据具体使用场景和对话历史灵活调整。

## 🚀 目标

通过此协议，实现：
- **极致的Token效率**：去除所有预定义术语表。
- **深度的AI参与**：AI不仅是使用者，更是意义的构建者。
- **高度的上下文关联**：强化AI对当前情境的专注和理解。

**AI请注意：** 当你看到`#术语`时，请结合当前协议的整体内容和我们的对话历史，尽力理解其在当前语境下的具体含义。

### 目的与功能

DPML术语定义协议用于在协议文件中嵌入标准化的术语定义，并提供统一的引用方式，解决以下关键问题：
- 为特定领域和上下文提供明确、原子化的术语定义
- 确保术语与其适用的协议/上下文紧密绑定
- 统一术语的引用方式，减少歧义
- 便于维护和更新术语定义

### 设计思想

术语定义协议基于以下核心理念：

1. **上下文绑定**：术语与其适用的协议文件紧密结合，确保术语在特定上下文中的含义明确。
2. **结构简洁**：采用最小必要的标签结构，便于维护和理解。
3. **引用明确**：使用特定符号系统引用术语，确保人类和AI都能识别术语引用。
4. **隐式作用域**：术语通过所在文件自动获得作用域，无需显式声明。
5. **自包含性**：协议文件包含其相关术语定义，提高文档完整性。

## 📝 语法定义

```ebnf
(* EBNF形式化定义 *)
terminologies_element ::= '<terminologies>' terminology+ '</terminologies>'
terminology_element ::= '<terminology>' terminology_content '</terminology>'
terminology_content ::= zh_element en_element definition_element examples_element
zh_element ::= '<zh>' text '</zh>'
en_element ::= '<en>' text '</en>'
definition_element ::= '<definition>' markdown_content '</definition>'
examples_element ::= '<examples>' example+ '</examples>'
example_element ::= '<example>' markdown_content '</example>'

text ::= (* 任何文本内容 *)
markdown_content ::= (* 任何有效的Markdown文本 *)
```

## 🧩 语义说明

### 术语定义结构

术语定义使用`<terminologies>`标签包含一组术语，每个术语使用`<terminology>`标签定义：

- **`<terminology>`**：单个术语的定义容器
- **`<zh>`**：术语的中文名称
- **`<en>`**：术语的英文名称
- **`<definition>`**：术语的详细定义，可同时包含AI理解和系统实现层面的解释
- **`<examples>`**：包含一个或多个`<example>`标签，提供使用示例

每个协议文件末尾使用标题"## 🔖 术语定义"引入术语定义部分，将术语定义与协议正文分隔。

### `#` 引用协议

术语定义协议规定了如何使用`#`符号作为统一的术语引用方式，遵循以下核心语法规则：

```ebnf
term_reference ::= '#' term_name [' ']
term_name ::= (* 术语的中文名称或英文名称 *)
```

#### 术语引用

术语引用在术语名称前使用井号标记：

| 语法 | 描述 | 示例 |
|------|------|------|
| `#术语` | 引用上下文中定义的术语 | `#协议绑定` |

基本术语引用在术语名称前使用井号，引用当前文档上下文中定义的术语：
```
#术语名称
```

例如：
- `#协议绑定` - 引用当前上下文中定义的"协议绑定"术语
- `#标签嵌套` - 引用当前上下文中定义的"标签嵌套"术语

推荐在术语后添加空格与其他内容分隔：`#术语 后续内容`，但这不是强制要求。

#### 与 Markdown 的兼容性

为了避免与 Markdown 的标题语法冲突，DPML 采用以下解析规则：

1. **行首 # 符号**：
   - 当 # 出现在行首时，按照 Markdown 语法解析为标题
   - 例如：`# 这是一个标题`

2. **非行首 # 符号**：
   - 当 # 出现在行中或词首时，解析为术语引用
   - 例如：`这里引用了一个 #术语`

3. **行首术语引用**：
   - 如果需要在行首使用术语引用，可以添加空格或其他字符
   - 例如：` #术语` 或 `文本 #术语`

这种设计保持了与 Markdown 的兼容性，同时提供了清晰的术语引用机制。

### 作用域规则

**隐式作用域**：术语自动具有其所在文档和上下文的作用域，这意味着：
- 术语的定义和使用范围由其所在的文档上下文决定
- 同一术语在不同上下文中可能有不同的定义
- 使用术语时应考虑当前所处的上下文环境
