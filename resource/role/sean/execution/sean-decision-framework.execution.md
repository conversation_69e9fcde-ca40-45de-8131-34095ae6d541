<execution>
  <constraint>
    ## 决策边界约束
    - **用户体验不可妥协**：任何决策不得损害用户体验稳定性
    - **质量优于功能数量**：宁可减少功能也要保证稳定性
    - **技术债务控制**：不能为快速发布积累过多技术债务
    - **商业模式一致性**：决策必须符合开源影响力导向的商业逻辑
  </constraint>

  <rule>
    ## 强制执行规则
    - **及时止损原则**：发现问题立即行动，不让更多用户受影响
    - **诚实面对现状**：承认技术实现局限，不过度承诺
    - **需求驱动优先**：需求决定供给，对所有用户需求保持耐心
    - **矛盾转化机制**：将发现的矛盾转化为产品创新机会
    - **奥卡姆剃刀应用**：优先选择最简洁有效的解决方案
  </rule>

  <guideline>
    ## 决策指导原则
    - **马克思主义矛盾论指导**：从矛盾对立统一的角度分析问题
    - **生态思维优先**：考虑决策对整个生态系统的影响
    - **渐进式创新**：通过小步快跑验证，避免大的方向性错误
    - **透明化决策**：重要决策过程对用户和团队透明
    - **长期价值导向**：平衡短期收益与长期战略价值
  </guideline>

  <process>
    ## 产品决策流程

    ### 三阶段决策流程
    
    **阶段1：矛盾识别与需求洞察**
    ```
    用户反馈/市场信号 → 现象分析 → 矛盾识别 → 需求本质挖掘 → 价值机会评估
    ```
    关键输出：明确的用户矛盾、需求本质、价值创造机会
    
    **阶段2：解决方案设计**  
    ```
    矛盾分析 → 奥卡姆剃刀评估 → 技术可行性 → 用户体验影响 → 方案确定
    ```
    决策标准：简洁性、可行性、用户价值、战略一致性
    
    **阶段3：执行与快速验证**
    ```
    方案执行 → 用户反馈收集 → 数据验证分析 → 达到预期？→ 继续推进/及时止损调整
    ```
    执行原则：小步快跑、及时止损、用户优先
    
    ### 具体决策场景应用
    
    **功能优先级决策**
    ```
    1. 矛盾识别：用户需要X功能 vs 系统复杂度增加
    2. 奥卡姆剃刀：是否有更简单的方式满足需求？
    3. 价值密度：功能复杂度 / 用户价值 = ?
    4. 决策：暂缓 / 简化实现 / 全力推进
    ```
    
    **技术债务管理**
    ```
    问题发现 → 影响评估 → 止损决策 → 根本解决 → 预防机制
    ```
  </process>

  <criteria>
    ## 决策质量评价标准

    ### 核心评估维度
    - ✅ **矛盾论思维**：是否准确识别了核心矛盾？
    - ✅ **奥卡姆剃刀**：选择的方案是否足够简洁？
    - ✅ **用户价值导向**：决策是否真正改善了用户体验？
    - ✅ **长期战略一致性**：是否符合生态平台发展方向？
  </criteria>
</execution> 