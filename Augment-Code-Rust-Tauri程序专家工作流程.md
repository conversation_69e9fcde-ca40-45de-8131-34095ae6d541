# Augment Code + Rust + Tauri 程序专家工作流程

## 核心工作原则
- **代码优先**：以高质量、可维护的代码为核心目标
- **架构清晰**：前后端分离，Rust后端 + Web前端的清晰架构
- **性能导向**：充分发挥Rust的性能优势和Tauri的轻量特性

## 标准工作流程

### 阶段1：需求分析与架构设计
1. **需求梳理**：分析用户功能需求，确定核心业务逻辑
2. **技术选型**：评估Tauri版本、前端框架选择（React/Vue/Vanilla）
3. **架构规划**：设计Rust后端API结构和前端组件架构
4. **依赖分析**：确定所需的Rust crates和前端依赖包

### 阶段2：开发环境搭建
1. **项目初始化**：使用`tauri init`创建项目骨架
2. **开发工具配置**：配置Rust analyzer、前端开发服务器
3. **构建配置**：设置tauri.conf.json和Cargo.toml
4. **代码规范**：配置rustfmt、clippy和前端代码格式化工具

### 阶段3：核心功能开发
1. **Rust后端开发**：实现核心业务逻辑和系统API
2. **Tauri命令定义**：创建前后端通信的command接口
3. **前端界面开发**：构建用户界面和交互逻辑
4. **数据流设计**：实现前后端数据传输和状态管理

### 阶段4：集成与优化
1. **功能集成测试**：验证前后端通信和业务流程
2. **性能优化**：优化Rust代码性能和前端渲染效率
3. **错误处理**：完善异常处理和用户友好的错误提示
4. **安全加固**：配置Tauri安全策略和权限控制

### 阶段5：构建与部署
1. **构建配置**：优化生产环境构建参数
2. **跨平台测试**：在目标平台上测试应用功能
3. **打包发布**：生成安装包和发布文件
4. **文档完善**：编写使用说明和开发文档

## 质量检查清单
- [ ] Rust代码通过clippy检查，无警告
- [ ] 前端代码符合ESLint/TypeScript规范
- [ ] 所有Tauri命令都有适当的错误处理
- [ ] 应用在目标平台上正常运行
- [ ] 性能指标满足预期要求
- [ ] 安全配置符合最佳实践

## 工作流程图

```mermaid
flowchart TD
    A[开始] --> B[阶段1：需求分析与架构设计]
    B --> C[阶段2：开发环境搭建]
    C --> D[阶段3：核心功能开发]
    D --> E[阶段4：集成与优化]
    E --> F[阶段5：构建与部署]
    F --> G[完成交付]
    
    B -.-> B1[需求梳理]
    B -.-> B2[技术选型]
    B -.-> B3[架构规划]
    
    C -.-> C1[项目初始化]
    C -.-> C2[开发工具配置]
    
    D -.-> D1[Rust后端开发]
    D -.-> D2[前端界面开发]
    
    E -.-> E1[功能集成测试]
    E -.-> E2[性能优化]
    
    F -.-> F1[构建配置]
    F -.-> F2[跨平台测试]
```

## 专业技术要点

### Rust后端开发重点
- **数据序列化**：使用`serde`进行JSON数据处理
- **异步编程**：合理使用`tokio`处理异步操作
- **错误处理**：利用`anyhow`或`thiserror`进行统一错误管理
- **API接口**：通过`tauri::command`暴露后端功能

### Tauri集成关键
- **安全配置**：在`tauri.conf.json`中设置适当的安全策略
- **前后端通信**：使用`invoke`方法调用Rust命令
- **窗口管理**：合理设置窗口属性和系统集成功能
- **插件扩展**：利用Tauri插件生态扩展应用能力

### 前端开发要点
- **类型安全**：使用TypeScript增强代码可靠性
- **组件架构**：合理组织组件结构和状态管理
- **用户体验**：优化界面响应性和交互流畅度
- **异步处理**：妥善处理异步操作和加载状态

## 常用命令参考

### 项目初始化
```bash
# 创建新的Tauri项目
npm create tauri-app@latest

# 或使用cargo
cargo install create-tauri-app
cargo create-tauri-app
```

### 开发调试
```bash
# 启动开发服务器
npm run tauri dev

# Rust代码检查
cargo clippy

# 代码格式化
cargo fmt
```

### 构建发布
```bash
# 构建生产版本
npm run tauri build

# 生成安装包
npm run tauri build -- --target x86_64-pc-windows-msvc
```

## 最佳实践建议

### 项目结构
```
src-tauri/
├── src/
│   ├── main.rs          # 主程序入口
│   ├── commands/        # Tauri命令模块
│   ├── models/          # 数据模型
│   └── utils/           # 工具函数
├── Cargo.toml
└── tauri.conf.json

src/
├── components/          # 前端组件
├── pages/              # 页面组件
├── utils/              # 前端工具
└── styles/             # 样式文件
```

### 性能优化
- 合理使用Rust的零成本抽象
- 避免不必要的数据克隆
- 优化前端资源加载和渲染
- 使用适当的缓存策略

### 安全考虑
- 限制Tauri API的使用范围
- 验证前端传入的数据
- 使用HTTPS进行网络通信
- 定期更新依赖包版本

这个工作流程专门针对Augment Code环境下的Rust + Tauri开发，提供了完整的开发指导和最佳实践建议。
