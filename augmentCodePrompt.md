

# Augment Code Prompt

## 新需求开发

### 🏆 核心目标
- **任务精细化分解**：将复杂需求分解为可执行的具体任务
- **成果复用最大化**：充分利用现有代码和组件，避免重复开发
- **架构持续优化**：识别重构机会，提升代码质量和可维护性
- **依赖关系清晰**：明确任务间的依赖关系和执行顺序
- **质量标准统一**：确保所有任务都符合既定的代码质量标准


### 📋 任务规划工作流程

#### 阶段1：现状分析与资产盘点
1. **代码库扫描**：分析现有代码结构、组件和工具函数
2. **功能模块识别**：识别已实现的功能模块和可复用组件
3. **技术债务评估**：发现需要重构的代码和架构问题
4. **依赖关系梳理**：分析模块间的依赖关系和耦合度

#### 阶段2：需求分析与任务分解
1. **需求理解**：深入理解用户需求和业务目标
2. **功能拆分**：将大功能拆分为独立的小功能模块
3. **复用机会识别**：识别可以复用现有代码的部分
4. **新开发需求确定**：明确需要全新开发的功能点

#### 阶段3：任务优先级规划
1. **依赖关系分析**：确定任务间的前置依赖关系
2. **风险评估**：识别高风险任务和技术难点
3. **价值评估**：评估每个任务的业务价值和技术价值
4. **优先级排序**：制定合理的任务执行顺序

#### 阶段4：重构优化任务制定
1. **代码重复检测**：识别重复或相似的代码片段
2. **抽象机会识别**：发现可以抽象为通用组件的代码
3. **性能优化点**：识别性能瓶颈和优化机会
4. **架构改进建议**：提出架构层面的改进方案

#### 阶段5：任务详细规划
1. **任务详细描述**：为每个任务编写详细的实现说明
2. **验收标准制定**：明确每个任务的完成标准
3. **时间估算**：评估每个任务的开发时间
4. **资源分配**：确定任务所需的技术资源和工具

## Bug修复

### 🏆 核心目标
- **根本原因分析**：深入分析错误的真正原因，而非表面现象
- **避坑知识积累**：将每次解决的问题转化为可复用的避坑经验
- **预防机制建立**：建立代码检查和预防机制，从源头避免问题
- **团队知识共享**：让个人踩坑经验成为团队共同财富


### 📋 错误分析与避坑流程

#### 阶段1：错误现象收集
1. **错误信息收集**：完整记录错误日志、堆栈信息和环境状态
2. **复现步骤记录**：详细记录触发错误的操作步骤
3. **环境信息采集**：记录操作系统、依赖版本、配置信息
4. **影响范围评估**：分析错误对系统功能的影响程度

#### 阶段2：根本原因分析
1. **症状vs原因分离**：区分错误的表面症状和深层原因
2. **调用链追踪**：追踪错误在代码中的传播路径
3. **依赖关系分析**：分析相关依赖和配置的影响
4. **设计缺陷识别**：识别可能的架构或设计问题

#### 阶段3：解决方案制定
1. **临时修复方案**：快速解决当前问题的临时方案
2. **根本解决方案**：从根本上解决问题的长期方案
3. **预防措施设计**：设计防止同类问题再次发生的机制
4. **测试验证策略**：确保解决方案有效性的测试方法

#### 阶段4：避坑知识记录
1. **问题档案建立**：建立结构化的问题记录档案
2. **避坑清单更新**：更新相关的代码检查清单
3. **最佳实践提炼**：从解决方案中提炼最佳实践
4. **团队知识分享**：将经验分享给团队成员

#### 阶段5：预防机制完善
1. **代码检查规则**：添加新的静态检查规则
2. **自动化测试**：增加相关的自动化测试用例
3. **文档更新**：更新开发规范和注意事项
4. **工具改进**：改进开发工具和流程