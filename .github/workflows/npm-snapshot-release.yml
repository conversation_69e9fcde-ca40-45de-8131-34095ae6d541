name: NPM Snapshot Release

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Feature branch to create snapshot from'
        required: true
        type: string

jobs:
  snapshot:
    name: Snapshot Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}
          fetch-depth: 0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org/'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests
        run: pnpm run test:ci

      - name: Release snapshot version
        run: |
          # 获取当前时间戳和短commit hash
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          SHORT_COMMIT=$(git rev-parse --short HEAD)
          BRANCH_NAME=$(echo "${{ github.event.inputs.branch }}" | sed 's/[^a-zA-Z0-9]/-/g')
          
          # 读取当前版本
          CURRENT_VERSION=$(node -p "require('./package.json').version.split('-')[0]")
          
          # 生成唯一的snapshot版本号
          SNAPSHOT_VERSION="${CURRENT_VERSION}-snapshot.${BRANCH_NAME}.${TIMESTAMP}"
          
          echo "生成snapshot版本号: $SNAPSHOT_VERSION"
          
          # 设置版本号
          npm version $SNAPSHOT_VERSION --no-git-tag-version
          
          # 发布snapshot版本
          pnpm publish --tag snapshot --no-git-checks
          
          echo "SNAPSHOT_VERSION=$SNAPSHOT_VERSION" >> $GITHUB_ENV
        env:
          NODE_AUTH_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}

      - name: Comment on PR
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            const version = process.env.SNAPSHOT_VERSION;
            const branch = '${{ github.event.inputs.branch }}';
            
            // 查找与此分支相关的PR
            const { data: prs } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              head: `${context.repo.owner}:${branch}`
            });
            
            const comment = `📸 **Snapshot版本已发布!**
            
            📦 版本号: \`${version}\`
            🌿 分支: \`${branch}\`
            
            🔗 安装命令:
            \`\`\`bash
            npx dpml-prompt@${version} <command>
            # 或
            npm install dpml-prompt@${version}
            \`\`\`
            
            💡 此snapshot版本用于测试特定功能分支的代码。`;
            
            if (prs.length > 0) {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: prs[0].number,
                body: comment
              });
            }