name: NPM Dev Release

on:
  push:
    branches:
      - develop
    paths-ignore:
      # GitHub相关配置
      - '.github/**'
      # 文档目录
      - 'docs/**'
      # 根目录的README文件（但不包括prompt/和resource/下的.md文件）
      - 'README*.md'
      - 'CHANGELOG.md'
      - 'TEST_ALPHA.md'
      # 配置文件
      - 'LICENSE'
      - '.gitignore'
      - '.npmignore'
      - '.cursorrules'
      - 'commitlint.config.js'
      # 资源文件
      - 'assets/**'
      # 测试覆盖率报告
      - 'coverage/**'

concurrency: ${{ github.workflow }}-${{ github.ref }}

permissions:
  contents: write
  pull-requests: write

jobs:
  snapshot:
    name: Dev Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org/'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests
        run: pnpm run test:ci

      - name: Release dev version
        run: |
          # 确保在正确的分支
          git checkout develop
          
          # 获取当前时间戳和短commit hash
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          SHORT_COMMIT=$(git rev-parse --short HEAD)
          
          # 读取当前版本，移除任何现有的snapshot标识
          CURRENT_VERSION=$(node -p "require('./package.json').version.split('-')[0]")
          
          # 生成唯一的dev版本号：base-dev.timestamp.commit
          DEV_VERSION="${CURRENT_VERSION}-dev.${TIMESTAMP}.${SHORT_COMMIT}"
          
          echo "生成dev版本号: $DEV_VERSION"
          
          # 直接设置版本号
          npm version $DEV_VERSION --no-git-tag-version
          
          # 使用pnpm发布dev版本
          pnpm publish --tag dev --no-git-checks
          
          # 输出版本信息供后续步骤使用
          echo "DEV_VERSION=$DEV_VERSION" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}

      - name: Comment on related PRs
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            const { execSync } = require('child_process');
            
            // 获取dev版本号
            const version = process.env.DEV_VERSION;
            
            // 查找相关的PR
            const { data: prs } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              base: 'develop'
            });
            
            const comment = `🚀 **Dev版本已发布!**
            
            📦 版本号: \`${version}\`
            🔗 安装命令: \`npx dpml-prompt@${version} <command>\`
            或者: \`npx dpml-prompt@dev <command>\`
            
            📚 使用示例:
            \`\`\`bash
            npx dpml-prompt@${version} hello
            npx dpml-prompt@${version} init
            npx dpml-prompt@${version} action <roleId>
            \`\`\`
            
            💡 你可以使用这个dev版本测试最新的develop分支功能。`;
            
            // 为每个相关PR添加评论
            for (const pr of prs) {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: pr.number,
                body: comment
              });
            } 